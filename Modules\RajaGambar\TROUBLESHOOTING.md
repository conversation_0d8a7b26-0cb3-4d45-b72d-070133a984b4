# Troubleshooting RajaG<PERSON>bar Module

## Ma<PERSON>ah <PERSON>um dan <PERSON>

### 1. Upload Area Tidak Bisa Diklik

**Gejala:**
- Area upload menampilkan "Klik untuk upload atau drag & drop"
- Tidak ada respons saat diklik
- File dialog tidak terbuka

**Kemungkinan Penyebab:**
1. Alpine.js tidak ter-load dengan benar
2. Event handler tidak terdaftar
3. Input file tidak memiliki referensi yang benar
4. CSS z-index menghalangi klik

**Solusi:**

#### A. Periksa Alpine.js
```bash
# Pastikan Alpine.js ter-load di browser
# Buka Developer Tools > Console
# Ketik: window.Alpine
# Harus mengembalikan object Alpine.js
```

#### B. Periksa Console Browser
```javascript
// Buka Developer Tools > Console
// Lihat apakah ada error JavaScript
// Periksa apakah console.log dari click handler muncul
```

#### C. Periksa HTML Structure
```html
<!-- Pastikan struktur HTML seperti ini: -->
<div x-data="{ ... }">
    <input x-ref="fileInput" type="file" ... />
    <div @click="$refs.fileInput.click()">
        <!-- Upload area -->
    </div>
</div>
```

#### D. Clear Cache
```bash
# Clear cache Laravel
php artisan cache:clear
php artisan view:clear
php artisan config:clear

# Clear browser cache
# Ctrl+F5 atau Cmd+Shift+R
```

### 2. File Tidak Ter-upload

**Gejala:**
- File dialog terbuka dan file dipilih
- Tidak ada preview yang muncul
- Data tidak tersimpan

**Kemungkinan Penyebab:**
1. wire:model tidak terhubung dengan benar
2. Livewire tidak ter-load
3. File size melebihi limit
4. File type tidak didukung

**Solusi:**

#### A. Periksa wire:model
```php
// Pastikan wire:model menggunakan path yang benar
wire:model="{{ $getStatePath() }}"
```

#### B. Periksa File Limits
```php
// Di config/rajagambar.php
'validation' => [
    'max_file_size' => 5120, // 5MB dalam KB
    'accepted_file_types' => [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp'
    ],
],
```

#### C. Periksa PHP Configuration
```ini
; Di php.ini
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
memory_limit = 256M
```

### 3. Preview Tidak Muncul

**Gejala:**
- File berhasil dipilih
- Loading indicator muncul
- Preview gambar tidak ditampilkan

**Kemungkinan Penyebab:**
1. URL preview tidak valid
2. Alpine.js state tidak update
3. CSS styling menghalangi preview

**Solusi:**

#### A. Debug Alpine.js State
```javascript
// Di browser console
// Periksa state Alpine.js
$el.__x.$data.state
$el.__x.$data.previewUrls
```

#### B. Periksa updatePreviews Method
```javascript
// Pastikan method updatePreviews dipanggil
// Lihat console.log di method tersebut
```

### 4. Error "Undefined constant 'index'"

**Gejala:**
- Error PHP di Blade template
- Halaman tidak ter-render

**Solusi:**
```php
// Gunakan x-text untuk output dinamis
<span x-text="'Gambar ' + (idx + 1)"></span>

// Bukan interpolasi Blade
{{ index + 1 }} // SALAH
```

### 5. Component Tidak Terdaftar

**Gejala:**
- Error "Class not found"
- Component tidak bisa digunakan di Form

**Solusi:**

#### A. Periksa Namespace
```php
use Modules\RajaGambar\Forms\Components\RajaGambarUpload;
```

#### B. Periksa Service Provider
```php
// Di RajaGambarServiceProvider
protected function registerFilamentComponents(): void
{
    // Component registration
}
```

#### C. Clear Autoload
```bash
composer dump-autoload
php artisan optimize:clear
```

## Debugging Tools

### 1. Browser Developer Tools
```javascript
// Console commands untuk debugging
console.log(window.Alpine);
console.log(window.Livewire);

// Inspect Alpine.js component
$el.__x.$data

// Inspect Livewire component
window.Livewire.find('component-id')
```

### 2. Laravel Debugging
```php
// Di component atau controller
dd($this->getState());
\Log::info('Upload data:', $data);

// Periksa file upload
dd(request()->file('gallery'));
```

### 3. Network Tab
- Periksa request/response upload
- Lihat error HTTP status
- Periksa payload data

## Langkah Troubleshooting Sistematis

### Step 1: Verifikasi Environment
```bash
# Periksa versi
php artisan --version
composer show filament/filament
composer show spatie/laravel-medialibrary

# Periksa konfigurasi
php artisan config:show rajagambar
```

### Step 2: Test Component Sederhana
```php
// Buat test form sederhana
RajaGambarUpload::make('test')
    ->label('Test Upload')
    ->acceptedFileTypes(['image/jpeg'])
    ->maxSize(1024)
```

### Step 3: Periksa Log Files
```bash
# Laravel logs
tail -f storage/logs/laravel.log

# Web server logs
tail -f /var/log/nginx/error.log
tail -f /var/log/apache2/error.log
```

### Step 4: Test Manual Upload
```php
// Test upload manual tanpa component
$file = request()->file('test');
if ($file) {
    $media = $model->addMediaFromRequest('test')
        ->toMediaCollection('test');
    dd($media);
}
```

## Kontak Support

Jika masalah masih berlanjut:

1. **Buat Issue Baru** dengan informasi:
   - Versi Laravel, FilamentPHP, dan RajaGambar
   - Browser dan versi
   - Error message lengkap
   - Langkah reproduksi

2. **Sertakan Log Files**:
   - Laravel log
   - Browser console log
   - Network tab screenshot

3. **Kode Minimal** yang menunjukkan masalah

---

*Troubleshooting guide ini akan terus diperbarui berdasarkan masalah yang ditemukan.*
