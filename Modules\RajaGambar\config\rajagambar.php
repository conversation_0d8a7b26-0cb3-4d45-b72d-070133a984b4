<?php

return [
    /*
    |--------------------------------------------------------------------------
    | RajaGambar Configuration
    |--------------------------------------------------------------------------
    |
    | Konfigurasi untuk modul RajaGambar yang mengatur pengaturan default
    | untuk upload, pemrosesan, dan penyimpanan gambar.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Default Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan default yang akan digunakan jika tidak ada konfigurasi
    | khusus yang diberikan pada component atau service.
    |
    */
    'defaults' => [
        'collection' => 'default',
        'disk' => 'public',
        'directory' => 'uploads',
        'max_file_size' => 10240, // KB (10MB)
        'image_quality' => 80,
        'resize_mode' => 'cover',
        'enable_conversions' => true,
        'enable_optimization' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Accepted File Types
    |--------------------------------------------------------------------------
    |
    | Tipe file yang diizinkan untuk upload. Anda bisa menambah atau
    | mengurangi tipe file sesuai kebutuhan aplikasi.
    |
    */
    'accepted_file_types' => [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml',
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Processing
    |--------------------------------------------------------------------------
    |
    | Konfigurasi untuk pemrosesan gambar seperti resize, crop, dan optimasi.
    |
    */
    'image_processing' => [
        'default_resize' => [
            'width' => 800,
            'height' => 600,
            'fit' => 'cover',
        ],

        'default_crop' => [
            'ratio' => '4:3',
        ],

        'quality_levels' => [
            'low' => 60,
            'medium' => 80,
            'high' => 90,
            'max' => 95,
        ],

        'fit_options' => [
            'crop' => 'Crop gambar untuk fit exact dimensions',
            'contain' => 'Fit gambar dalam dimensions tanpa crop',
            'cover' => 'Cover seluruh area dengan possible crop',
            'fill' => 'Fill area dengan stretch jika perlu',
            'fillMax' => 'Fill maksimal tanpa exceed original size',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Media Collections
    |--------------------------------------------------------------------------
    |
    | Definisi media collections yang tersedia dan konfigurasinya.
    | Setiap collection bisa memiliki pengaturan yang berbeda.
    |
    */
    'collections' => [
        'default' => [
            'single_file' => true,
            'accepted_mime_types' => [
                'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'
            ],
            'conversions' => [
                'thumb' => [
                    'width' => 300,
                    'height' => 300,
                    'fit' => 'crop',
                    'quality' => 80,
                ],
            ],
        ],

        'gallery' => [
            'single_file' => false,
            'accepted_mime_types' => [
                'image/jpeg', 'image/png', 'image/gif', 'image/webp'
            ],
            'conversions' => [
                'thumb' => [
                    'width' => 300,
                    'height' => 300,
                    'fit' => 'crop',
                    'quality' => 80,
                ],
                'medium' => [
                    'width' => 600,
                    'height' => 450,
                    'fit' => 'contain',
                    'quality' => 85,
                ],
            ],
        ],

        'avatars' => [
            'single_file' => true,
            'accepted_mime_types' => [
                'image/jpeg', 'image/png'
            ],
            'conversions' => [
                'thumb' => [
                    'width' => 150,
                    'height' => 150,
                    'fit' => 'crop',
                    'quality' => 90,
                ],
                'medium' => [
                    'width' => 300,
                    'height' => 300,
                    'fit' => 'crop',
                    'quality' => 90,
                ],
            ],
        ],

        'banners' => [
            'single_file' => false,
            'accepted_mime_types' => [
                'image/jpeg', 'image/png', 'image/webp'
            ],
            'conversions' => [
                'thumb' => [
                    'width' => 400,
                    'height' => 133,
                    'fit' => 'crop',
                    'quality' => 80,
                ],
                'medium' => [
                    'width' => 800,
                    'height' => 267,
                    'fit' => 'crop',
                    'quality' => 85,
                ],
            ],
        ],

        'products' => [
            'single_file' => false,
            'accepted_mime_types' => [
                'image/jpeg', 'image/png', 'image/webp'
            ],
            'conversions' => [
                'thumb' => [
                    'width' => 200,
                    'height' => 200,
                    'fit' => 'crop',
                    'quality' => 80,
                ],
                'medium' => [
                    'width' => 400,
                    'height' => 400,
                    'fit' => 'contain',
                    'quality' => 85,
                ],
                'large' => [
                    'width' => 800,
                    'height' => 800,
                    'fit' => 'contain',
                    'quality' => 90,
                ],
            ],
        ],

        'cms' => [
            'single_file' => false,
            'accepted_mime_types' => [
                'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'
            ],
            'conversions' => [
                'thumb' => [
                    'width' => 300,
                    'height' => 300,
                    'fit' => 'crop',
                    'quality' => 80,
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Component Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan khusus untuk RajaGambarUpload component.
    |
    */
    'component' => [
        'default_preview_height' => '200px',
        'show_file_info' => true,
        'enable_drag_drop' => true,
        'show_progress' => true,
        'auto_upload' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Storage Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan penyimpanan file dan media.
    |
    */
    'storage' => [
        'default_disk' => 'public',
        'path_prefix' => 'uploads',
        'create_directories' => true,
        'directory_permissions' => 0755,
        'file_permissions' => 0644,
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan untuk optimasi performa.
    |
    */
    'performance' => [
        'queue_conversions' => false,
        'queue_name' => 'default',
        'timeout' => 300, // seconds
        'memory_limit' => '256M',
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan keamanan untuk upload file.
    |
    */
    'security' => [
        'scan_uploads' => false,
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
        'blocked_extensions' => ['php', 'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js'],
        'max_filename_length' => 255,
        'sanitize_filename' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Rules
    |--------------------------------------------------------------------------
    |
    | Aturan validasi default untuk upload file.
    |
    */
    'validation' => [
        'max_file_size' => 10240, // KB
        'min_width' => 100,
        'min_height' => 100,
        'max_width' => 4000,
        'max_height' => 4000,
        'aspect_ratio_tolerance' => 0.1,
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Messages
    |--------------------------------------------------------------------------
    |
    | Pesan error yang akan ditampilkan untuk berbagai kondisi.
    |
    */
    'messages' => [
        'upload_failed' => 'Gagal mengupload file. Silakan coba lagi.',
        'invalid_file_type' => 'Tipe file tidak diizinkan.',
        'file_too_large' => 'Ukuran file terlalu besar.',
        'processing_failed' => 'Gagal memproses gambar.',
        'conversion_failed' => 'Gagal membuat konversi gambar.',
    ],
];
