<?php

// Bootstrap Laravel
require_once __DIR__ . '/../../bootstrap/app.php';
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Debug test untuk RajaGambarUpload component
echo "=== RajaGambar Component Debug Test ===\n";

// 1. Check if class exists
$componentClass = \Modules\RajaGambar\Forms\Components\RajaGambarUpload::class;
echo "1. Component class exists: " . (class_exists($componentClass) ? "✅ YES" : "❌ NO") . "\n";

// 2. Check if service exists
$serviceClass = \Modules\RajaGambar\Services\SpatieMediaService::class;
echo "2. Service class exists: " . (class_exists($serviceClass) ? "✅ YES" : "❌ NO") . "\n";

// 3. Check if view exists
$viewPath = module_path('RajaGambar', 'resources/views/components/forms/raja-gambar-upload.blade.php');
echo "3. View file exists: " . (file_exists($viewPath) ? "✅ YES" : "❌ NO") . "\n";

// 4. Check if provider is loaded
$providers = app()->getLoadedProviders();
$providerLoaded = isset($providers[\Modules\RajaGambar\Providers\RajaGambarServiceProvider::class]);
echo "4. Service Provider loaded: " . ($providerLoaded ? "✅ YES" : "❌ NO") . "\n";

// 5. Try to instantiate component
try {
    $component = \Modules\RajaGambar\Forms\Components\RajaGambarUpload::make('test');
    echo "5. Component instantiation: ✅ SUCCESS\n";
    echo "   - Component type: " . get_class($component) . "\n";
} catch (Exception $e) {
    echo "5. Component instantiation: ❌ FAILED\n";
    echo "   - Error: " . $e->getMessage() . "\n";
}

// 6. Check Filament Forms
echo "6. Filament Forms available: " . (class_exists(\Filament\Forms\Components\FileUpload::class) ? "✅ YES" : "❌ NO") . "\n";

echo "\n=== End Debug Test ===\n";
