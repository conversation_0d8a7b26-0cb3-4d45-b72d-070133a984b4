<?php

namespace Modules\RajaGambar\Tests\Unit;

use Tests\TestCase;
use Modules\RajaGambar\Services\SpatieMediaService;
use Modules\RajaGambar\Models\RajaGambar;
use Illuminate\Http\UploadedFile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;

/**
 * Test untuk SpatieMediaService
 * 
 * Catatan: Test ini hanya sebagai contoh struktur testing.
 * Untuk implementasi lengkap, perlu setup database dan media library yang proper.
 */
class SpatieMediaServiceTest extends TestCase
{
    use RefreshDatabase;

    protected SpatieMediaService $mediaService;
    protected RajaGambar $model;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Setup fake storage
        Storage::fake('public');
        
        // Initialize service
        $this->mediaService = app(SpatieMediaService::class);
        
        // Create test model (perlu disesuaikan dengan struktur database Anda)
        // $this->model = RajaGambar::factory()->create();
    }

    /** @test */
    public function it_can_check_valid_image_formats()
    {
        // Test valid image
        $validImage = UploadedFile::fake()->image('test.jpg', 800, 600);
        $this->assertTrue($this->mediaService->isValidImage($validImage));

        // Test invalid file (jika ada method untuk test invalid file)
        $invalidFile = UploadedFile::fake()->create('test.txt', 100);
        // $this->assertFalse($this->mediaService->isValidImage($invalidFile));
    }

    /** @test */
    public function it_returns_supported_formats()
    {
        $formats = $this->mediaService->getSupportedFormats();
        
        $this->assertIsArray($formats);
        $this->assertArrayHasKey('jpeg', $formats);
        $this->assertArrayHasKey('png', $formats);
        $this->assertArrayHasKey('gif', $formats);
        $this->assertArrayHasKey('webp', $formats);
        $this->assertEquals('image/jpeg', $formats['jpeg']);
    }

    /** @test */
    public function it_can_generate_unique_filename()
    {
        $file = UploadedFile::fake()->image('test-image.jpg', 800, 600);
        
        // Menggunakan reflection untuk test protected method
        $reflection = new \ReflectionClass($this->mediaService);
        $method = $reflection->getMethod('generateFileName');
        $method->setAccessible(true);
        
        $filename1 = $method->invoke($this->mediaService, $file);
        $filename2 = $method->invoke($this->mediaService, $file);
        
        // Filename harus unik
        $this->assertNotEquals($filename1, $filename2);
        $this->assertStringEndsWith('.jpg', $filename1);
        $this->assertStringEndsWith('.jpg', $filename2);
    }

    /** @test */
    public function it_can_generate_custom_filename()
    {
        $file = UploadedFile::fake()->image('test-image.jpg', 800, 600);
        $options = ['custom_name' => 'my-custom-image'];
        
        $reflection = new \ReflectionClass($this->mediaService);
        $method = $reflection->getMethod('generateFileName');
        $method->setAccessible(true);
        
        $filename = $method->invoke($this->mediaService, $file, $options);
        
        $this->assertEquals('my-custom-image.jpg', $filename);
    }

    /** @test */
    public function it_sanitizes_filename()
    {
        $file = UploadedFile::fake()->image('test image with spaces & symbols!.jpg', 800, 600);
        
        $reflection = new \ReflectionClass($this->mediaService);
        $method = $reflection->getMethod('generateFileName');
        $method->setAccessible(true);
        
        $filename = $method->invoke($this->mediaService, $file);
        
        // Filename tidak boleh mengandung karakter khusus
        $this->assertStringNotContainsString(' ', $filename);
        $this->assertStringNotContainsString('&', $filename);
        $this->assertStringNotContainsString('!', $filename);
        $this->assertStringContainsString('_', $filename); // Spasi diganti underscore
    }

    /**
     * Test yang memerlukan database dan model setup
     * Uncomment dan sesuaikan jika database sudah ready
     */
    
    /*
    /** @test */
    /*
    public function it_can_upload_single_media()
    {
        $file = UploadedFile::fake()->image('test.jpg', 800, 600);
        
        $media = $this->mediaService->uploadMedia($this->model, $file, 'default');
        
        $this->assertInstanceOf(\Spatie\MediaLibrary\MediaCollections\Models\Media::class, $media);
        $this->assertEquals('default', $media->collection_name);
    }
    */

    /*
    /** @test */
    /*
    public function it_can_upload_multiple_media()
    {
        $files = [
            UploadedFile::fake()->image('test1.jpg', 800, 600),
            UploadedFile::fake()->image('test2.jpg', 800, 600),
        ];
        
        $mediaCollection = $this->mediaService->uploadMedia($this->model, $files, 'gallery');
        
        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $mediaCollection);
        $this->assertCount(2, $mediaCollection);
    }
    */

    /*
    /** @test */
    /*
    public function it_can_get_media_from_model()
    {
        // Upload media first
        $file = UploadedFile::fake()->image('test.jpg', 800, 600);
        $this->mediaService->uploadMedia($this->model, $file, 'default');
        
        // Get media
        $media = $this->mediaService->getMedia($this->model, 'default');
        $firstMedia = $this->mediaService->getFirstMedia($this->model, 'default');
        
        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $media);
        $this->assertInstanceOf(\Spatie\MediaLibrary\MediaCollections\Models\Media::class, $firstMedia);
    }
    */

    /*
    /** @test */
    /*
    public function it_can_delete_media()
    {
        // Upload media first
        $file = UploadedFile::fake()->image('test.jpg', 800, 600);
        $media = $this->mediaService->uploadMedia($this->model, $file, 'default');
        
        // Delete media
        $result = $this->mediaService->deleteMedia($media);
        
        $this->assertTrue($result);
    }
    */

    /*
    /** @test */
    /*
    public function it_can_clear_media_collection()
    {
        // Upload multiple media
        $files = [
            UploadedFile::fake()->image('test1.jpg', 800, 600),
            UploadedFile::fake()->image('test2.jpg', 800, 600),
        ];
        $this->mediaService->uploadMedia($this->model, $files, 'gallery');
        
        // Clear collection
        $result = $this->mediaService->clearMediaCollection($this->model, 'gallery');
        
        $this->assertTrue($result);
        
        // Verify collection is empty
        $media = $this->mediaService->getMedia($this->model, 'gallery');
        $this->assertCount(0, $media);
    }
    */

    /*
    /** @test */
    /*
    public function it_can_update_media_properties()
    {
        // Upload media first
        $file = UploadedFile::fake()->image('test.jpg', 800, 600);
        $media = $this->mediaService->uploadMedia($this->model, $file, 'default');
        
        // Update properties
        $updatedMedia = $this->mediaService->updateMedia($media, [
            'name' => 'Updated Name',
            'custom_properties' => ['alt' => 'Updated Alt Text']
        ]);
        
        $this->assertEquals('Updated Name', $updatedMedia->name);
        $this->assertEquals('Updated Alt Text', $updatedMedia->custom_properties['alt']);
    }
    */

    protected function tearDown(): void
    {
        // Cleanup
        Storage::fake('public');
        parent::tearDown();
    }
}
