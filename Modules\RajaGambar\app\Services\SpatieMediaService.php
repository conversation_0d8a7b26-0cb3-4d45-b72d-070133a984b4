<?php

namespace Modules\RajaGambar\Services;

use Mo<PERSON>les\RajaGambar\Models\RajaGambar;
use Spatie\MediaLibrary\MediaCollections\Models\Media as SpatieMedia;
use Spatie\Image\Enums\Fit;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Exception;

class SpatieMediaService
{
    /**
     * Upload dan simpan gambar ke model RajaGambar
     *
     * @param RajaGambar $model
     * @param UploadedFile|array $files
     * @param string $collection
     * @param array $options
     * @return SpatieMedia|Collection
     */
    public function uploadMedia(RajaGambar $model, $files, string $collection = 'default', array $options = [])
    {
        try {
            // Jika single file
            if ($files instanceof UploadedFile) {
                return $this->processSingleFile($model, $files, $collection, $options);
            }

            // Jika multiple files
            if (is_array($files)) {
                return $this->processMultipleFiles($model, $files, $collection, $options);
            }

            throw new Exception('Invalid file format provided');
        } catch (Exception $e) {
            throw new Exception('Failed to upload media: ' . $e->getMessage());
        }
    }

    /**
     * Proses single file upload
     *
     * @param RajaGambar $model
     * @param UploadedFile $file
     * @param string $collection
     * @param array $options
     * @return SpatieMedia
     */
    protected function processSingleFile(RajaGambar $model, UploadedFile $file, string $collection, array $options = []): SpatieMedia
    {
        $mediaAdder = $model->addMedia($file)
            ->usingFileName($this->generateFileName($file, $options))
            ->toMediaCollection($collection);

        return $mediaAdder;
    }

    /**
     * Proses multiple files upload
     *
     * @param RajaGambar $model
     * @param array $files
     * @param string $collection
     * @param array $options
     * @return Collection
     */
    protected function processMultipleFiles(RajaGambar $model, array $files, string $collection, array $options = []): Collection
    {
        $uploadedMedia = collect();

        foreach ($files as $file) {
            if ($file instanceof UploadedFile) {
                $media = $this->processSingleFile($model, $file, $collection, $options);
                $uploadedMedia->push($media);
            }
        }

        return $uploadedMedia;
    }

    /**
     * Generate nama file yang unik
     *
     * @param UploadedFile $file
     * @param array $options
     * @return string
     */
    protected function generateFileName(UploadedFile $file, array $options = []): string
    {
        $extension = $file->getClientOriginalExtension();
        $baseName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        
        // Sanitize filename
        $baseName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $baseName);
        
        if (isset($options['custom_name'])) {
            return $options['custom_name'] . '.' . $extension;
        }

        return $baseName . '_' . time() . '_' . uniqid() . '.' . $extension;
    }



    /**
     * Resize gambar dengan dimensi tertentu
     *
     * @param RajaGambar $model
     * @param string $conversionName
     * @param int $width
     * @param int $height
     * @param string $fit
     * @param int $quality
     * @return $this
     */
    public function addResizeConversion(RajaGambar $model, string $conversionName, int $width, int $height, string $fit = 'crop', int $quality = 80): self
    {
        $fitEnum = match($fit) {
            'contain' => Fit::Contain,
            'cover' => Fit::Cover,
            'fill' => Fit::Fill,
            'fillMax' => Fit::FillMax,
            default => Fit::Crop,
        };

        $model->addMediaConversion($conversionName)
            ->fit($fitEnum, $width, $height)
            ->quality($quality)
            ->nonQueued();

        return $this;
    }

    /**
     * Crop gambar dengan ratio tertentu
     *
     * @param RajaGambar $model
     * @param string $conversionName
     * @param int $width
     * @param int $height
     * @param int $quality
     * @return $this
     */
    public function addCropConversion(RajaGambar $model, string $conversionName, int $width, int $height, int $quality = 80): self
    {
        $model->addMediaConversion($conversionName)
            ->fit(Fit::Crop, $width, $height)
            ->quality($quality)
            ->nonQueued();

        return $this;
    }

    /**
     * Optimize gambar untuk web
     *
     * @param RajaGambar $model
     * @param string $conversionName
     * @param int $quality
     * @return $this
     */
    public function addOptimizedConversion(RajaGambar $model, string $conversionName, int $quality = 80): self
    {
        $model->addMediaConversion($conversionName)
            ->quality($quality)
            ->optimize()
            ->nonQueued();

        return $this;
    }

    /**
     * Retrieve media dari model
     *
     * @param RajaGambar $model
     * @param string $collection
     * @return Collection
     */
    public function getMedia(RajaGambar $model, string $collection = 'default'): Collection
    {
        return $model->getMedia($collection);
    }

    /**
     * Get single media dari collection
     *
     * @param RajaGambar $model
     * @param string $collection
     * @return SpatieMedia|null
     */
    public function getFirstMedia(RajaGambar $model, string $collection = 'default'): ?SpatieMedia
    {
        return $model->getFirstMedia($collection);
    }

    /**
     * Update media properties
     *
     * @param SpatieMedia $media
     * @param array $properties
     * @return SpatieMedia
     */
    public function updateMedia(SpatieMedia $media, array $properties): SpatieMedia
    {
        if (isset($properties['name'])) {
            $media->name = $properties['name'];
        }

        if (isset($properties['custom_properties'])) {
            $media->custom_properties = array_merge(
                $media->custom_properties ?? [],
                $properties['custom_properties']
            );
        }

        $media->save();

        return $media;
    }

    /**
     * Delete media
     *
     * @param SpatieMedia $media
     * @return bool
     */
    public function deleteMedia(SpatieMedia $media): bool
    {
        try {
            $media->delete();
            return true;
        } catch (Exception) {
            return false;
        }
    }

    /**
     * Delete semua media dari collection
     *
     * @param RajaGambar $model
     * @param string $collection
     * @return bool
     */
    public function clearMediaCollection(RajaGambar $model, string $collection): bool
    {
        try {
            $model->clearMediaCollection($collection);
            return true;
        } catch (Exception) {
            return false;
        }
    }

    /**
     * Get URL media dengan conversion
     *
     * @param SpatieMedia $media
     * @param string $conversion
     * @return string
     */
    public function getMediaUrl(SpatieMedia $media, string $conversion = ''): string
    {
        return $media->getUrl($conversion);
    }

    /**
     * Check apakah file adalah gambar yang valid
     *
     * @param UploadedFile $file
     * @return bool
     */
    public function isValidImage(UploadedFile $file): bool
    {
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
        return in_array($file->getMimeType(), $allowedMimes);
    }

    /**
     * Get supported image formats
     *
     * @return array
     */
    public function getSupportedFormats(): array
    {
        return [
            'jpeg' => 'image/jpeg',
            'jpg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'svg' => 'image/svg+xml',
        ];
    }
}
