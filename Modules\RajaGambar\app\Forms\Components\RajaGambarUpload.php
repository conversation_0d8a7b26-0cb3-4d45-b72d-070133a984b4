<?php

namespace Modules\RajaGambar\Forms\Components;

use Filament\Forms\Components\FileUpload;

class RajaGambarUpload extends FileUpload
{
    protected string $view = 'rajagambar::components.forms.raja-gambar-upload';

    // Properties untuk image processing
    protected ?array $resizeDimensions = null;
    protected ?array $cropDimensions = null;
    protected bool $optimizeImage = false;
    protected int $imageQuality = 80;
    protected string $mediaCollection = 'default';
    protected bool $enableConversions = true;
    protected array $customConversions = [];

    /**
     * Initialize component
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Set default configuration
        $this->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'])
            ->image()
            ->imageResizeMode('cover')
            ->imageCropAspectRatio('1:1')
            ->imageResizeTargetWidth('800')
            ->imageResizeTargetHeight('600')
            ->maxSize(10240); // 10MB
    }

    /**
     * Set resize dimensions untuk gambar
     *
     * @param int $width
     * @param int $height
     * @param string $fit
     * @return $this
     */
    public function resize(int $width, int $height, string $fit = 'crop'): static
    {
        $this->resizeDimensions = [
            'width' => $width,
            'height' => $height,
            'fit' => $fit,
        ];

        // Update FilamentPHP resize settings
        $this->imageResizeTargetWidth($width)
            ->imageResizeTargetHeight($height)
            ->imageResizeMode($fit);

        return $this;
    }

    /**
     * Set crop dimensions/ratio untuk gambar
     *
     * @param int|string $width
     * @param int|null $height
     * @return $this
     */
    public function crop($width, ?int $height = null): static
    {
        if (is_string($width) && str_contains($width, ':')) {
            // Aspect ratio format (e.g., "16:9", "1:1")
            $this->imageCropAspectRatio($width);
            $this->cropDimensions = ['ratio' => $width];
        } else {
            // Specific dimensions
            $this->cropDimensions = [
                'width' => $width,
                'height' => $height ?? $width,
            ];
            
            if ($height) {
                $ratio = $width . ':' . $height;
                $this->imageCropAspectRatio($ratio);
            }
        }

        return $this;
    }

    /**
     * Enable image optimization
     *
     * @param int $quality
     * @return $this
     */
    public function optimize(int $quality = 80): static
    {
        $this->optimizeImage = true;
        $this->imageQuality = $quality;

        // Set FilamentPHP image quality
        $this->imagePreviewHeight('200');

        return $this;
    }

    /**
     * Set media collection untuk Spatie Media Library
     *
     * @param string $collection
     * @return $this
     */
    public function collection(string $collection): static
    {
        $this->mediaCollection = $collection;
        return $this;
    }

    /**
     * Set image quality
     *
     * @param int $quality
     * @return $this
     */
    public function quality(int $quality): static
    {
        $this->imageQuality = $quality;
        return $this;
    }

    /**
     * Enable/disable automatic conversions
     *
     * @param bool $enable
     * @return $this
     */
    public function conversions(bool $enable = true): static
    {
        $this->enableConversions = $enable;
        return $this;
    }

    /**
     * Add custom conversion
     *
     * @param string $name
     * @param array $options
     * @return $this
     */
    public function addConversion(string $name, array $options = []): static
    {
        $this->customConversions[$name] = $options;
        return $this;
    }



    /**
     * Enable image preview
     *
     * @param string|int $height
     * @return $this
     */
    public function preview($height = '200px'): static
    {
        if (is_numeric($height)) {
            $height = $height . 'px';
        }
        
        $this->imagePreviewHeight($height);
        return $this;
    }



    /**
     * Get resize dimensions
     *
     * @return array|null
     */
    public function getResizeDimensions(): ?array
    {
        return $this->resizeDimensions;
    }

    /**
     * Get crop dimensions
     *
     * @return array|null
     */
    public function getCropDimensions(): ?array
    {
        return $this->cropDimensions;
    }

    /**
     * Check if optimization is enabled
     *
     * @return bool
     */
    public function isOptimizeEnabled(): bool
    {
        return $this->optimizeImage;
    }

    /**
     * Get image quality
     *
     * @return int
     */
    public function getImageQuality(): int
    {
        return $this->imageQuality;
    }

    /**
     * Get media collection
     *
     * @return string
     */
    public function getMediaCollection(): string
    {
        return $this->mediaCollection;
    }

    /**
     * Get custom conversions
     *
     * @return array
     */
    public function getCustomConversions(): array
    {
        return $this->customConversions;
    }

    /**
     * Check if conversions are enabled
     *
     * @return bool
     */
    public function isConversionsEnabled(): bool
    {
        return $this->enableConversions;
    }

    /**
     * Process uploaded files dengan Spatie Media Library
     *
     * @return mixed
     */
    public function processUploadedFiles()
    {
        // Custom processing logic bisa ditambahkan di sini
        // untuk integrasi dengan SpatieMediaService

        return $this->getState();
    }

    /**
     * Static method untuk membuat instance baru
     *
     * @param string $name
     * @return static
     */
    public static function make(string $name): static
    {
        $static = app(static::class, ['name' => $name]);
        $static->configure();

        return $static;
    }
}
