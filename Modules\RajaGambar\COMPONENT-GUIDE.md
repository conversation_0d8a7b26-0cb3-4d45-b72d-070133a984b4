# RajaGambarUpload Component Guide

## Cara Kerja Component

### 1. Upload Process Flow

```
User Action → File Selection → Livewire Processing → Preview Update
```

#### Step by Step:
1. **User clicks upload area** → File dialog opens
2. **User selects files** → `handleFileSelect()` triggered
3. **Livewire processes files** → Creates temporary `livewire-file:` URLs
4. **Component updates state** → `updatePreviews()` called
5. **Final URLs generated** → Real URLs replace temporary ones

### 2. Preview System

#### Temporary Files (During Upload)
```javascript
// Livewire temporary file format
"livewire-file:JzBnSaNJc37hrkr7HV4U6nWhVGjxZV-metaam9hbi10cmFuLXJlRXlTRmFkeUpRLXVuc3BsYXNoLmpwZw==-.jpg"
```

**Problem**: <PERSON><PERSON><PERSON> cannot display `livewire-file:` URLs directly
**Solution**: Show upload placeholder until real URL is available

#### Final URLs (After Upload)
```javascript
// Real URLs after processing
"/storage/media/1/image.jpg"
"https://example.com/images/photo.png"
```

### 3. Component States

#### Loading State
```javascript
isLoading: true  // Shows loading overlay
```

#### Preview States
```javascript
previewUrls: []           // No files
previewUrls: [null]       // Uploading (livewire-file:)
previewUrls: ["/url.jpg"] // Uploaded successfully
```

## Troubleshooting Common Issues

### Issue 1: "previewUrl is not defined"
**Cause**: Alpine.js variable scope in `x-for` loop
**Solution**: Use simple variable names like `(url, i)`

```html
<!-- ❌ Wrong -->
<template x-for="(previewUrl, index) in previewUrls">

<!-- ✅ Correct -->
<template x-for="(url, i) in previewUrls">
```

### Issue 2: "Failed to execute 'createObjectURL'"
**Cause**: Trying to create blob URL from string
**Solution**: Only use `createObjectURL()` with File objects

```javascript
// ❌ Wrong
URL.createObjectURL("livewire-file:...")

// ✅ Correct
if (file instanceof File) {
    URL.createObjectURL(file)
}
```

### Issue 3: Images not showing during upload
**Cause**: `livewire-file:` URLs cannot be displayed
**Solution**: Show placeholder during upload

```html
<template x-if="!url || url.startsWith('livewire-file:')">
    <div>Mengupload...</div>
</template>
```

## Configuration Examples

### Basic Upload
```php
RajaGambarUpload::make('image')
    ->label('Upload Gambar')
    ->collection('default')
```

### Multiple Files with Processing
```php
RajaGambarUpload::make('gallery')
    ->label('Galeri Foto')
    ->collection('gallery')
    ->multiple()
    ->maxFiles(10)
    ->acceptedFileTypes(['image/jpeg', 'image/png'])
    ->maxSize(5120) // 5MB
    ->resize(1024, 768)
    ->optimize(85)
```

### Advanced Configuration
```php
RajaGambarUpload::make('photos')
    ->label('Foto Produk')
    ->collection('products')
    ->multiple()
    ->maxFiles(5)
    ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
    ->maxSize(2048) // 2MB
    ->resize(800, 600)
    ->crop('16:9')
    ->optimize(90)
    ->quality(85)
    ->format('webp')
    ->columnSpanFull()
```

## Alpine.js Methods

### updatePreviews()
Updates preview URLs from component state
```javascript
updatePreviews() {
    // Filters out livewire-file: URLs
    // Only shows real URLs
}
```

### handleFileSelect(event)
Handles file selection from input
```javascript
handleFileSelect(event) {
    this.isLoading = true;
    // Let Livewire handle the processing
    setTimeout(() => {
        this.isLoading = false;
        this.updatePreviews();
    }, 2000);
}
```

### handleDrop(event)
Handles drag & drop files
```javascript
handleDrop(event) {
    // Transfer files to input element
    // Trigger change event for Livewire
}
```

### removeFile(i)
Removes file from state
```javascript
removeFile(i) {
    if (Array.isArray(this.state)) {
        this.state.splice(i, 1);
    } else {
        this.state = null;
    }
    this.updatePreviews();
}
```

## CSS Classes

### Upload Area
```css
.cursor-pointer          /* Clickable cursor */
.border-dashed          /* Dashed border */
.hover:border-primary-500 /* Hover effect */
```

### Preview Grid
```css
.grid-cols-2            /* 2 columns on mobile */
.md:grid-cols-3         /* 3 columns on tablet */
.lg:grid-cols-4         /* 4 columns on desktop */
```

### Loading Overlay
```css
.absolute.inset-0       /* Full coverage */
.bg-white/80           /* Semi-transparent */
.backdrop-blur-sm      /* Blur effect */
```

## Integration with FilamentPHP

### Form Schema
```php
use Modules\RajaGambar\Forms\Components\RajaGambarUpload;

public function form(Form $form): Form
{
    return $form->schema([
        RajaGambarUpload::make('images')
            ->label('Upload Images')
            ->collection('gallery')
            ->multiple()
    ]);
}
```

### Resource Integration
```php
// In your Resource class
public static function form(Form $form): Form
{
    return $form->schema([
        Section::make('Media')
            ->schema([
                RajaGambarUpload::make('featured_image')
                    ->label('Featured Image')
                    ->collection('featured'),
                    
                RajaGambarUpload::make('gallery')
                    ->label('Gallery')
                    ->collection('gallery')
                    ->multiple()
                    ->columnSpanFull(),
            ])
    ]);
}
```

## Best Practices

### 1. Always Set Collection
```php
// ✅ Good
->collection('gallery')

// ❌ Avoid
// No collection specified
```

### 2. Set Reasonable File Limits
```php
->maxFiles(10)        // Reasonable limit
->maxSize(5120)       // 5MB max
```

### 3. Optimize Images
```php
->resize(1024, 768)   // Reasonable size
->optimize(85)        // Good quality/size balance
```

### 4. Use Appropriate File Types
```php
->acceptedFileTypes([
    'image/jpeg',
    'image/png',
    'image/webp'      // Modern format
])
```

### 5. Handle Errors Gracefully
```javascript
@error="console.warn('Image load error:', $event.target.src)"
```

## Performance Tips

1. **Lazy Loading**: Use `loading="lazy"` on images
2. **Optimize Images**: Always use `->optimize()` method
3. **Reasonable Sizes**: Don't allow huge uploads
4. **WebP Format**: Use modern formats when possible
5. **Limit Files**: Set `maxFiles()` appropriately

## Security Considerations

1. **File Type Validation**: Always validate file types
2. **Size Limits**: Set reasonable size limits
3. **Sanitize Filenames**: Component handles this automatically
4. **Storage Security**: Use proper storage permissions

---

*Component guide ini akan terus diperbarui sesuai perkembangan fitur.*
