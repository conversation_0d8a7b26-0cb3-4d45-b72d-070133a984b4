<?php

use Illuminate\Support\Facades\Route;
use Modules\RajaGambar\Forms\Components\RajaGambarUpload;

Route::get('/test-rajagambar', function () {
    try {
        $component = RajaGambarUpload::make('test_upload')
            ->label('Test Upload')
            ->collection('test')
            ->multiple()
            ->maxFiles(5);
            
        return response()->json([
            'status' => 'success',
            'component_class' => get_class($component),
            'view_path' => $component->getView(),
            'state_path' => $component->getStatePath(),
        ]);
    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ], 500);
    }
});
