# Changelog

All notable changes to the RajaGambar module will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2025-06-27

### Added
- **SpatieMediaService**: Comprehensive service class for image management
  - Upload single and multiple files
  - Image processing (resize, crop, optimize)
  - Media conversions with custom dimensions
  - CRUD operations for media files
  - Support for multiple media collections
  - File validation and format checking
  - Custom filename generation with sanitization

- **RajaGambarUpload**: Custom FilamentPHP component
  - Extends base FileUpload component
  - Method chaining support for easy configuration
  - Image processing options (resize, crop, optimize)
  - Media collection management
  - Quality settings and conversions
  - Preview functionality
  - Drag & drop interface with Alpine.js

- **Configuration System**
  - Comprehensive config file (`config/rajagambar.php`)
  - Default settings for all operations
  - Media collection definitions
  - Image processing parameters
  - Security and validation rules
  - Performance optimization settings
  - Error message customization

- **Helper Functions**
  - Global helper functions for easy access
  - Upload functions (`raja_gambar_upload`, `raja_gambar_batch_upload`)
  - Image retrieval functions (`raja_gambar_url`, `raja_gambar_urls`, `raja_gambar_first`, `raja_gambar_all`)
  - Utility functions (`raja_gambar_has`, `raja_gambar_count`, `raja_gambar_validate`)
  - HTML generation functions (`raja_gambar_img`, `raja_gambar_gallery`, `raja_gambar_responsive`)
  - Management functions (`raja_gambar_delete`, `raja_gambar_clear`)

- **RajaGambarHelper Class**
  - Static helper methods for common operations
  - Image URL generation
  - HTML creation for galleries and single images
  - Responsive image support
  - Batch operations with progress tracking
  - Configuration access methods

- **Blade Component View**
  - Custom view template for RajaGambarUpload component
  - Alpine.js integration for interactive features
  - Drag & drop file upload interface
  - Image preview with loading states
  - Responsive grid layout for multiple images
  - Processing options display
  - File management UI with delete functionality

- **Service Provider Integration**
  - Automatic service registration as singleton
  - Helper functions auto-loading
  - Configuration publishing
  - FilamentPHP component registration
  - Module auto-discovery support

- **Testing Structure**
  - Unit test template for SpatieMediaService
  - Test methods for all major functionality
  - Mock data and file upload testing
  - Database integration test examples

- **Documentation**
  - Comprehensive README with usage examples
  - Method chaining examples
  - Configuration documentation
  - Helper function reference
  - Installation and setup guide
  - Example resource implementation

- **Example Files**
  - ExampleResource showing FilamentPHP integration
  - Method chaining examples
  - Various configuration patterns
  - Simple and advanced usage scenarios

### Features
- **Multi-format Support**: JPEG, PNG, GIF, WebP, SVG
- **Image Processing**: Automatic resize, crop, and optimization
- **Media Collections**: Organized file management with custom settings
- **Conversions**: Automatic thumbnail and size variant generation
- **Validation**: File type, size, and dimension validation
- **Security**: Filename sanitization and extension blocking
- **Performance**: Configurable memory limits and timeout settings
- **Responsive**: Mobile-friendly upload interface
- **Accessibility**: Screen reader friendly components
- **Extensible**: Easy to extend and customize

### Technical Details
- **Laravel 11** compatibility
- **FilamentPHP 3.2** integration
- **Spatie Media Library** foundation
- **Alpine.js** for frontend interactions
- **Blade Components** for reusable UI
- **Service Provider** pattern for registration
- **Singleton** service registration for performance
- **Method Chaining** for fluent API
- **PSR-4** autoloading compliance
- **Namespace** organization following Laravel conventions

### Configuration Options
- **Default Settings**: Collection, disk, quality, resize mode
- **File Types**: Configurable accepted MIME types
- **Processing**: Resize dimensions, crop ratios, quality levels
- **Collections**: Per-collection settings and conversions
- **Security**: File validation and sanitization rules
- **Performance**: Queue settings and resource limits
- **Messages**: Customizable error messages

### Breaking Changes
- None (initial release)

### Deprecated
- None (initial release)

### Removed
- None (initial release)

### Fixed
- None (initial release)

### Security
- File type validation to prevent malicious uploads
- Filename sanitization to prevent directory traversal
- Extension blocking for executable files
- File size limits to prevent resource exhaustion
- MIME type validation for additional security