<?php


use Illuminate\Support\Facades\Route;





Route::get('/cetak/struk/{penjualanId}', [App\Http\Controllers\CetakController::class, 'cetakStruk'])
    ->name('cetak.struk');

Route::post('/cetak/struk/template', [App\Http\Controllers\CetakController::class, 'cetakStrukDenganTemplate'])
    ->name('cetak.struk.template');


// Route untuk upload gambar TinyMCE
Route::post('/upload-tinymce', [App\Http\Controllers\TinyMceController::class, 'upload'])
    ->middleware(['auth', 'bypass.auth'])
    ->name('tinymce.upload');

// Include test routes
require __DIR__.'/test-component.php';

// Route untuk upload gambar RajaEditor
Route::post('/upload-rajaeditor', [App\Http\Controllers\ModulUploadController::class, 'upload'])
    ->middleware(['auth'])
    ->name('rajaeditor.upload');




\Laravel\Folio\Folio::path(resource_path('views/testing'))->uri('testing');

// TIDAK PERLU MEMBUAT ROUTE UNTUK TESTING, KARENA SUDAH MEMAKAI PACKAGE FOLIO

 
// Route untuk testing flyout navigation
Route::get('/test-flyout', function () {
    return view('test-flyout');
})->name('test.flyout');

// Route untuk testing auto-discovery navigation
Route::get('/test-auto-discovery', function () {
    return view('test-auto-discovery');
})->name('test.auto-discovery');

