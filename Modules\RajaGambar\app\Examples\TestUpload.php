<?php

namespace Modules\RajaGambar\Examples;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Modules\RajaGambar\Forms\Components\RajaGambarUpload;

class TestUpload extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-photo';
    
    protected static string $view = 'rajagambar::test-upload';
    
    protected static ?string $title = 'Test RajaGambar Upload';
    
    public ?array $data = [];
    
    public function mount(): void
    {
        $this->form->fill();
    }
    
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Test Upload')
                    ->description('Test RajaGambarUpload component')
                    ->schema([
                        // Test basic upload
                        RajaGambarUpload::make('single_image')
                            ->label('Single Image')
                            ->collection('test')
                            ->acceptedFileTypes(['image/jpeg', 'image/png'])
                            ->maxSize(2048) // 2MB
                            ->resize(800, 600)
                            ->optimize(85),
                            
                        // Test multiple upload
                        RajaGambarUpload::make('multiple_images')
                            ->label('Multiple Images')
                            ->collection('gallery')
                            ->multiple()
                            ->maxFiles(5)
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif'])
                            ->maxSize(5120) // 5MB
                            ->resize(1024, 768)
                            ->crop('4:3')
                            ->optimize(80)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ])
            ->statePath('data');
    }
    
    public function save(): void
    {
        $data = $this->form->getState();
        
        // Debug output
        dd($data);
    }
}
