{{-- 
    RajaGambarUpload Component View
    Custom FilamentPHP FileUpload component dengan integrasi Spatie Media Library
--}}

<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    <div
        x-data="{
            state: $wire.$entangle('{{ $getStatePath() }}'),
            isLoading: false,
            previewUrls: [],

            init() {
                this.updatePreviews();
                this.$watch('state', () => this.updatePreviews());
            },

            updatePreviews() {
                // Sederhana: hanya tampilkan preview untuk data yang sudah ada
                this.previewUrls = [];

                if (Array.isArray(this.state)) {
                    this.state.forEach(file => {
                        if (typeof file === 'string' && file.length > 0) {
                            this.previewUrls.push(file);
                        } else if (file && typeof file === 'object' && (file.url || file.preview)) {
                            this.previewUrls.push(file.url || file.preview);
                        }
                    });
                } else if (this.state) {
                    if (typeof this.state === 'string' && this.state.length > 0) {
                        this.previewUrls.push(this.state);
                    } else if (typeof this.state === 'object' && (this.state.url || this.state.preview)) {
                        this.previewUrls.push(this.state.url || this.state.preview);
                    }
                }
            },
            
            handleFileSelect(event) {
                this.isLoading = true;

                // Biarkan Livewire menangani file processing
                // Preview akan diupdate otomatis melalui state watcher
                setTimeout(() => {
                    this.isLoading = false;
                    this.updatePreviews();
                }, 2000);
            },
            
            removeFile(index) {
                if (Array.isArray(this.state)) {
                    this.state.splice(index, 1);
                } else {
                    this.state = null;
                }
                this.updatePreviews();
            },

            handleDrop(event) {
                const files = Array.from(event.dataTransfer.files);
                if (files.length > 0) {
                    // Simulasi file input change event
                    const fileInput = this.$refs.fileInput;

                    try {
                        const dataTransfer = new DataTransfer();
                        files.forEach(file => dataTransfer.items.add(file));
                        fileInput.files = dataTransfer.files;

                        // Trigger change event untuk Livewire
                        fileInput.dispatchEvent(new Event('change', { bubbles: true }));
                    } catch (error) {
                        console.warn('Drag & drop error:', error);
                    }
                }
            }
        }"
        class="raja-gambar-upload-wrapper"
    >
        {{-- Loading Overlay --}}
        <div 
            x-show="isLoading" 
            x-transition
            class="absolute inset-0 bg-white/80 dark:bg-gray-900/80 flex items-center justify-center z-10 rounded-lg"
        >
            <div class="flex items-center space-x-2">
                <svg class="animate-spin h-5 w-5 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-sm text-gray-600 dark:text-gray-400">Memproses gambar...</span>
            </div>
        </div>

        {{-- File Upload Area --}}
        <div class="relative">
            {{-- Default FilamentPHP FileUpload Component --}}
            <x-filament::input.wrapper
                :disabled="$isDisabled()"
                :valid="! $errors->has($getStatePath())"
                class="relative"
            >
                <input
                    x-ref="fileInput"
                    type="file"
                    wire:model="{{ $getStatePath() }}"
                    {!! $getExtraInputAttributeBag()->class([
                        'absolute inset-0 z-10 h-full w-full cursor-pointer opacity-0',
                        'cursor-not-allowed' => $isDisabled(),
                    ]) !!}
                    @if ($isMultiple())
                        multiple
                    @endif
                    @if ($getAcceptedFileTypes())
                        accept="{{ implode(',', $getAcceptedFileTypes()) }}"
                    @endif
                    @change="handleFileSelect($event)"
                    {{ $isDisabled() ? 'disabled' : '' }}
                />

                {{-- Upload Area UI --}}
                <div
                    class="flex flex-col items-center justify-center p-6 text-center border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 transition-colors cursor-pointer"
                    @click="$refs.fileInput.click()"
                    @dragover.prevent
                    @dragenter.prevent
                    @drop.prevent="handleDrop($event)"
                >
                    {{-- Upload Icon --}}
                    <svg class="w-8 h-8 mb-2 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>

                    {{-- Upload Text --}}
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        <span class="font-medium text-primary-600 dark:text-primary-400">Klik untuk upload</span>
                        atau drag & drop
                    </div>

                    {{-- File Info --}}
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        @if ($getAcceptedFileTypes())
                            {{ implode(', ', array_map(fn($type) => strtoupper(str_replace('image/', '', $type)), $getAcceptedFileTypes())) }}
                        @endif
                        @if ($getMaxSize())
                            (Max: {{ number_format($getMaxSize() / 1024, 1) }}MB)
                        @endif
                    </div>
                </div>
            </x-filament::input.wrapper>
        </div>

        {{-- Preview Area --}}
        <div x-show="previewUrls.length > 0" x-transition class="mt-4">
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <template x-for="(previewUrl, index) in previewUrls" :key="index">
                    <div class="relative group">
                        {{-- Image Preview --}}
                        <div class="aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
                            <img
                                :src="previewUrl"
                                :alt="'Preview ' + (index + 1)"
                                class="w-full h-full object-cover"
                                loading="lazy"
                            />
                        </div>

                        {{-- Remove Button --}}
                        <button
                            type="button"
                            @click="removeFile(index)"
                            class="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            title="Hapus gambar"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>

                        {{-- Image Info Overlay --}}
                        <div class="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <div class="truncate">
                                <span x-text="'Gambar ' + (index + 1)"></span>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        {{-- Processing Options Info --}}
        @if ($getResizeDimensions() || $getCropDimensions() || $isOptimizeEnabled())
            <div class="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div class="text-sm text-blue-800 dark:text-blue-200">
                    <div class="font-medium mb-1">Pengaturan Pemrosesan:</div>
                    <ul class="text-xs space-y-1">
                        @if ($getResizeDimensions())
                            <li>• Resize: {{ $getResizeDimensions()['width'] }}x{{ $getResizeDimensions()['height'] }} ({{ $getResizeDimensions()['fit'] }})</li>
                        @endif
                        @if ($getCropDimensions())
                            <li>• Crop: 
                                @if (isset($getCropDimensions()['ratio']))
                                    {{ $getCropDimensions()['ratio'] }}
                                @else
                                    {{ $getCropDimensions()['width'] }}x{{ $getCropDimensions()['height'] }}
                                @endif
                            </li>
                        @endif
                        @if ($isOptimizeEnabled())
                            <li>• Optimasi: Kualitas {{ $getImageQuality() }}%</li>
                        @endif
                        <li>• Koleksi: {{ $getMediaCollection() }}</li>
                    </ul>
                </div>
            </div>
        @endif
    </div>
</x-dynamic-component>

{{-- Custom Styles --}}
<style>
.raja-gambar-upload-wrapper {
    position: relative;
}

.raja-gambar-upload-wrapper .aspect-square {
    aspect-ratio: 1 / 1;
}

.raja-gambar-upload-wrapper img {
    transition: transform 0.2s ease-in-out;
}

.raja-gambar-upload-wrapper img:hover {
    transform: scale(1.05);
}
</style>
