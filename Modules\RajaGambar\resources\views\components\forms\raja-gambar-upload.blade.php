<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
    <div x-data="{
        state: $wire.$entangle('{{ $getStatePath() }}'),
        isLoading: false,
        previewUrls: [],
        
        init() {
            console.log('RajaGambarUpload initialized');
            console.log('Initial state:', this.state);
            this.updatePreviews();
            this.$watch('state', (newState) => {
                console.log('State changed:', newState);
                this.updatePreviews();
            });
        },
        
        updatePreviews() {
            this.previewUrls = [];
            console.log('Updating previews, state:', this.state);
            
            if (Array.isArray(this.state)) {
                this.state.forEach((file, index) => {
                    const url = this.getPreviewUrl(file);
                    console.log('File ' + index + ':', file, 'Preview URL:', url);
                    if (url) this.previewUrls.push(url);
                });
            } else if (this.state) {
                const url = this.getPreviewUrl(this.state);
                console.log('Single file:', this.state, 'Preview URL:', url);
                if (url) this.previewUrls.push(url);
            }
            
            console.log('Final preview URLs:', this.previewUrls);
        },
        
        getPreviewUrl(file) {
            if (!file) return null;
            
            if (typeof file === 'string') {
                if (file.startsWith('livewire-file:')) {
                    return '/livewire/preview-file/' + file.replace('livewire-file:', '');
                }
                return file;
            }
            
            if (typeof file === 'object') {
                return file.url || file.preview || null;
            }
            
            return null;
        },
        
        handleFileSelect(event) {
            console.log('File selected:', event.target.files);
            this.isLoading = true;
            
            setTimeout(() => {
                this.isLoading = false;
                this.updatePreviews();
                console.log('File processing completed');
            }, 2000);
        },
        
        removeFile(i) {
            if (Array.isArray(this.state)) {
                this.state.splice(i, 1);
            } else {
                this.state = null;
            }
            this.updatePreviews();
        },
        
        handleDrop(event) {
            const files = Array.from(event.dataTransfer.files);
            if (files.length > 0) {
                const fileInput = this.$refs.fileInput;
                try {
                    const dataTransfer = new DataTransfer();
                    files.forEach(file => dataTransfer.items.add(file));
                    fileInput.files = dataTransfer.files;
                    fileInput.dispatchEvent(new Event('change', { bubbles: true }));
                } catch (error) {
                    console.warn('Drag & drop error:', error);
                }
            }
        }
    }" class="raja-gambar-upload-wrapper">

        <div x-show="isLoading" x-transition class="absolute inset-0 bg-white/80 flex items-center justify-center z-10 rounded-lg">
            <div class="flex items-center space-x-2">
                <svg class="animate-spin h-5 w-5 text-primary-600" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-sm text-gray-600">Memproses gambar...</span>
            </div>
        </div>

        <div class="relative">
            <x-filament::input.wrapper :disabled="$isDisabled()" :valid="! $errors->has($getStatePath())" class="relative">
                <input
                    x-ref="fileInput"
                    type="file"
                    wire:model="{{ $getStatePath() }}"
                    {!! $getExtraInputAttributeBag()->class(['absolute inset-0 z-10 h-full w-full cursor-pointer opacity-0']) !!}
                    @if ($isMultiple()) multiple @endif
                    @if ($getAcceptedFileTypes()) accept="{{ implode(',', $getAcceptedFileTypes()) }}" @endif
                    @change="handleFileSelect($event)"
                />

                <div class="flex flex-col items-center justify-center p-6 text-center border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-500 transition-colors cursor-pointer"
                     @click="$refs.fileInput.click()"
                     @dragover.prevent
                     @dragenter.prevent
                     @drop.prevent="handleDrop($event)">
                    <svg class="w-8 h-8 mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <div class="text-sm text-gray-600">
                        <span class="font-medium text-primary-600">Klik untuk upload</span> atau drag & drop
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                        @if ($getAcceptedFileTypes())
                            {{ implode(', ', array_map(fn($type) => strtoupper(str_replace('image/', '', $type)), $getAcceptedFileTypes())) }}
                        @endif
                        @if ($getMaxSize())
                            (Max: {{ number_format($getMaxSize() / 1024, 1) }}MB)
                        @endif
                    </div>
                </div>
            </x-filament::input.wrapper>
        </div>

        <div x-show="previewUrls.length > 0" x-transition class="mt-4">
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <template x-for="(url, i) in previewUrls" :key="i">
                    <div class="relative group">
                        <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                            <template x-if="url">
                                <img :src="url" :alt="'Preview ' + (i + 1)" class="w-full h-full object-cover" loading="lazy" 
                                     @error="console.log('Image load error for:', url)" />
                            </template>
                            <template x-if="!url">
                                <div class="w-full h-full flex items-center justify-center">
                                    <div class="text-center">
                                        <svg class="w-8 h-8 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <div class="text-xs text-gray-500">Mengupload...</div>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <button type="button" @click="removeFile(i)" class="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity" title="Hapus gambar">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                        <div class="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <div class="truncate">
                                <span x-text="'Gambar ' + (i + 1)"></span>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        @if ($getResizeDimensions() || $getCropDimensions() || $isOptimizeEnabled())
            <div class="mt-3 p-3 bg-blue-50 rounded-lg">
                <div class="text-sm text-blue-800">
                    <div class="font-medium mb-1">Pengaturan Pemrosesan:</div>
                    <ul class="text-xs space-y-1">
                        @if ($getResizeDimensions())
                            <li>• Resize: {{ $getResizeDimensions()['width'] }}x{{ $getResizeDimensions()['height'] }}</li>
                        @endif
                        @if ($getCropDimensions())
                            <li>• Crop:
                                @if (isset($getCropDimensions()['ratio']))
                                    {{ $getCropDimensions()['ratio'] }}
                                @else
                                    {{ $getCropDimensions()['width'] }}x{{ $getCropDimensions()['height'] }}
                                @endif
                            </li>
                        @endif
                        @if ($isOptimizeEnabled())
                            <li>• Optimasi: Kualitas {{ $getImageQuality() }}%</li>
                        @endif
                        <li>• Koleksi: {{ $getMediaCollection() }}</li>
                    </ul>
                </div>
            </div>
        @endif
    </div>
</x-dynamic-component>
