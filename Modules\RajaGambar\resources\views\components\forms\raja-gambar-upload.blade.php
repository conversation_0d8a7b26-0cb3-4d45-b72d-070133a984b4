<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
    <div x-data="{
        state: $wire.$entangle('{{ $getStatePath() }}'),
        isLoading: false,
        previewUrls: [],
        
        init() {
            console.log('RajaGambarUpload initialized');
            this.updatePreviews();
            this.$watch('state', () => this.updatePreviews());
        },
        
        updatePreviews() {
            this.previewUrls = [];
            if (Array.isArray(this.state)) {
                this.state.forEach((file, index) => {
                    const url = this.getPreviewUrl(file);
                    if (url) this.previewUrls.push(url);
                });
            } else if (this.state) {
                const url = this.getPreviewUrl(this.state);
                if (url) this.previewUrls.push(url);
            }
        },
        
        getPreviewUrl(file) {
            if (!file) return null;
            if (typeof file === 'string') {
                if (file.startsWith('livewire-file:')) {
                    return '/livewire/preview-file/' + file.replace('livewire-file:', '');
                }
                return file;
            }
            if (typeof file === 'object') {
                return file.url || file.preview || null;
            }
            return null;
        },
        
        handleFileSelect(event) {
            console.log('File selected:', event.target.files);
            this.isLoading = true;
            setTimeout(() => {
                this.isLoading = false;
                this.updatePreviews();
            }, 2000);
        },
        
        removeFile(i) {
            if (Array.isArray(this.state)) {
                this.state.splice(i, 1);
            } else {
                this.state = null;
            }
        }
    }">

        <div class="relative">
            <x-filament::input.wrapper :disabled="$isDisabled()" :valid="! $errors->has($getStatePath())">
                <input
                    type="file"
                    wire:model="{{ $getStatePath() }}"
                    {!! $getExtraInputAttributeBag()->class(['absolute inset-0 z-10 h-full w-full cursor-pointer opacity-0']) !!}
                    @if ($isMultiple()) multiple @endif
                    @if ($getAcceptedFileTypes()) accept="{{ implode(',', $getAcceptedFileTypes()) }}" @endif
                    @change="handleFileSelect($event)"
                />

                <div class="flex flex-col items-center justify-center p-6 text-center border-2 border-dashed border-gray-300 rounded-lg">
                    <svg class="w-8 h-8 mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <div class="text-sm text-gray-600">
                        <span class="font-medium text-primary-600">Klik untuk upload</span> atau drag & drop
                    </div>
                </div>
            </x-filament::input.wrapper>
        </div>

        <div x-show="previewUrls.length > 0" class="mt-4">
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <template x-for="(url, i) in previewUrls" :key="i">
                    <div class="relative group">
                        <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                            <img :src="url" class="w-full h-full object-cover" />
                        </div>
                        <button type="button" @click="removeFile(i)" class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </template>
            </div>
        </div>

    </div>
</x-dynamic-component>
