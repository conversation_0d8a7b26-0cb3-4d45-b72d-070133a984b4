# Security Policy

## Supported Versions

We actively support the following versions of the RajaGambar module:

| Version | Supported          |
| ------- | ------------------ |
| 1.0.x   | :white_check_mark: |

## Security Features

The RajaGambar module includes several built-in security features:

### File Upload Security

1. **File Type Validation**
   - MIME type checking for uploaded files
   - Extension validation against allowed list
   - Blocked extensions for executable files

2. **File Size Limits**
   - Configurable maximum file size limits
   - Prevention of resource exhaustion attacks
   - Memory limit protection during processing

3. **Filename Sanitization**
   - Automatic sanitization of uploaded filenames
   - Prevention of directory traversal attacks
   - Removal of dangerous characters

4. **Content Validation**
   - Image format verification
   - File header validation
   - Malicious content detection

### Configuration Security

1. **Default Secure Settings**
   - Conservative default file size limits
   - Restricted file type acceptance
   - Secure storage configurations

2. **Validation Rules**
   - Comprehensive validation for all inputs
   - Dimension and aspect ratio validation
   - Quality and processing parameter limits

### Storage Security

1. **Disk Configuration**
   - Support for secure storage disks
   - Configurable visibility settings
   - Directory permission management

2. **Path Security**
   - Secure path generation
   - Prevention of path manipulation
   - Isolated storage locations

## Security Best Practices

When using the RajaGambar module, follow these security best practices:

### 1. File Upload Configuration

```php
// In config/rajagambar.php
'security' => [
    'scan_uploads' => true,
    'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    'blocked_extensions' => ['php', 'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js'],
    'max_filename_length' => 255,
    'sanitize_filename' => true,
],
```

### 2. Storage Configuration

```php
// Use secure disk configuration
'storage' => [
    'default_disk' => 'private', // Use private disk for sensitive files
    'directory_permissions' => 0755,
    'file_permissions' => 0644,
],
```

### 3. Validation Rules

```php
// Implement strict validation
'validation' => [
    'max_file_size' => 5120, // 5MB limit
    'min_width' => 100,
    'min_height' => 100,
    'max_width' => 2000,
    'max_height' => 2000,
],
```

### 4. User Input Validation

Always validate user inputs before processing:

```php
// Validate files before upload
if (!raja_gambar_validate($uploadedFile)) {
    throw new InvalidArgumentException('Invalid file format');
}

// Use type hints and validation
public function uploadImage(UploadedFile $file, string $collection = 'default')
{
    // Validation logic here
}
```

### 5. Access Control

Implement proper access control:

```php
// Check user permissions before allowing uploads
if (!auth()->user()->can('upload_images')) {
    abort(403, 'Unauthorized');
}

// Validate ownership before operations
if (!$model->belongsToUser(auth()->user())) {
    abort(403, 'Access denied');
}
```

### 6. Error Handling

Handle errors securely without exposing sensitive information:

```php
try {
    $media = raja_gambar_upload($model, $file);
} catch (Exception $e) {
    // Log the actual error
    Log::error('Upload failed: ' . $e->getMessage());

    // Return generic error to user
    return response()->json(['error' => 'Upload failed'], 500);
}
```

## Reporting Security Vulnerabilities

We take security vulnerabilities seriously. If you discover a security vulnerability in the RajaGambar module, please follow these steps:

### 1. Do Not Create Public Issues

Please **DO NOT** create public GitHub issues for security vulnerabilities. This could put users at risk.

### 2. Report Privately

Send security reports to:
- **Email**: [Create a private issue or contact maintainers]
- **Subject**: "SECURITY: RajaGambar Module Vulnerability"

### 3. Include Details

Please include the following information in your report:
- Description of the vulnerability
- Steps to reproduce the issue
- Potential impact assessment
- Suggested fix (if available)
- Your contact information

### 4. Response Timeline

We will acknowledge receipt of your vulnerability report within 48 hours and provide a detailed response within 7 days indicating the next steps in handling your report.