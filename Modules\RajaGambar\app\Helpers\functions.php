<?php

use <PERSON><PERSON><PERSON>\RajaGambar\Helpers\RajaGambarHelper;
use Mo<PERSON><PERSON>\RajaGambar\Models\RajaGambar;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

if (!function_exists('raja_gambar_upload')) {
    /**
     * Upload gambar menggunakan RajaGambar helper
     *
     * @param RajaGambar $model
     * @param mixed $files
     * @param string $collection
     * @param array $options
     * @return Media|\Illuminate\Support\Collection
     */
    function raja_gambar_upload(RajaGambar $model, $files, string $collection = 'default', array $options = [])
    {
        return RajaGambarHelper::upload($model, $files, $collection, $options);
    }
}

if (!function_exists('raja_gambar_url')) {
    /**
     * Get URL gambar dari model
     *
     * @param RajaGambar $model
     * @param string $collection
     * @param string $conversion
     * @return string|null
     */
    function raja_gambar_url(RajaGambar $model, string $collection = 'default', string $conversion = ''): ?string
    {
        return RajaGambarHelper::getImageUrl($model, $collection, $conversion);
    }
}

if (!function_exists('raja_gambar_urls')) {
    /**
     * Get multiple image URLs dari model
     *
     * @param RajaGambar $model
     * @param string $collection
     * @param string $conversion
     * @return array
     */
    function raja_gambar_urls(RajaGambar $model, string $collection = 'default', string $conversion = ''): array
    {
        return RajaGambarHelper::getImageUrls($model, $collection, $conversion);
    }
}

if (!function_exists('raja_gambar_has')) {
    /**
     * Check apakah model memiliki gambar
     *
     * @param RajaGambar $model
     * @param string $collection
     * @return bool
     */
    function raja_gambar_has(RajaGambar $model, string $collection = 'default'): bool
    {
        return RajaGambarHelper::hasImages($model, $collection);
    }
}

if (!function_exists('raja_gambar_count')) {
    /**
     * Get jumlah gambar dalam collection
     *
     * @param RajaGambar $model
     * @param string $collection
     * @return int
     */
    function raja_gambar_count(RajaGambar $model, string $collection = 'default'): int
    {
        return RajaGambarHelper::getImageCount($model, $collection);
    }
}

if (!function_exists('raja_gambar_first')) {
    /**
     * Get gambar pertama dari collection
     *
     * @param RajaGambar $model
     * @param string $collection
     * @return Media|null
     */
    function raja_gambar_first(RajaGambar $model, string $collection = 'default'): ?Media
    {
        return RajaGambarHelper::getFirstImage($model, $collection);
    }
}

if (!function_exists('raja_gambar_all')) {
    /**
     * Get semua gambar dari collection
     *
     * @param RajaGambar $model
     * @param string $collection
     * @return \Illuminate\Support\Collection
     */
    function raja_gambar_all(RajaGambar $model, string $collection = 'default')
    {
        return RajaGambarHelper::getImages($model, $collection);
    }
}

if (!function_exists('raja_gambar_delete')) {
    /**
     * Delete gambar
     *
     * @param Media $media
     * @return bool
     */
    function raja_gambar_delete(Media $media): bool
    {
        return RajaGambarHelper::deleteImage($media);
    }
}

if (!function_exists('raja_gambar_clear')) {
    /**
     * Clear semua gambar dari collection
     *
     * @param RajaGambar $model
     * @param string $collection
     * @return bool
     */
    function raja_gambar_clear(RajaGambar $model, string $collection): bool
    {
        return RajaGambarHelper::clearImages($model, $collection);
    }
}

if (!function_exists('raja_gambar_gallery')) {
    /**
     * Create image gallery HTML
     *
     * @param RajaGambar $model
     * @param string $collection
     * @param string $conversion
     * @param array $options
     * @return string
     */
    function raja_gambar_gallery(RajaGambar $model, string $collection = 'gallery', string $conversion = 'thumb', array $options = []): string
    {
        return RajaGambarHelper::createGallery($model, $collection, $conversion, $options);
    }
}

if (!function_exists('raja_gambar_img')) {
    /**
     * Create single image HTML
     *
     * @param RajaGambar $model
     * @param string $collection
     * @param string $conversion
     * @param array $attributes
     * @return string
     */
    function raja_gambar_img(RajaGambar $model, string $collection = 'default', string $conversion = '', array $attributes = []): string
    {
        return RajaGambarHelper::createImage($model, $collection, $conversion, $attributes);
    }
}

if (!function_exists('raja_gambar_responsive')) {
    /**
     * Create responsive image HTML
     *
     * @param RajaGambar $model
     * @param string $collection
     * @param array $conversions
     * @param array $attributes
     * @return string
     */
    function raja_gambar_responsive(RajaGambar $model, string $collection = 'default', array $conversions = [], array $attributes = []): string
    {
        return RajaGambarHelper::createResponsiveImage($model, $collection, $conversions, $attributes);
    }
}

if (!function_exists('raja_gambar_info')) {
    /**
     * Get image info
     *
     * @param Media $media
     * @return array
     */
    function raja_gambar_info(Media $media): array
    {
        return RajaGambarHelper::getImageInfo($media);
    }
}

if (!function_exists('raja_gambar_config')) {
    /**
     * Get RajaGambar configuration
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function raja_gambar_config(string $key, $default = null)
    {
        return RajaGambarHelper::config($key, $default);
    }
}

if (!function_exists('raja_gambar_formats')) {
    /**
     * Get supported image formats
     *
     * @return array
     */
    function raja_gambar_formats(): array
    {
        return RajaGambarHelper::getSupportedFormats();
    }
}

if (!function_exists('raja_gambar_validate')) {
    /**
     * Validate image file
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @return bool
     */
    function raja_gambar_validate($file): bool
    {
        return RajaGambarHelper::isValidImage($file);
    }
}

if (!function_exists('raja_gambar_batch_upload')) {
    /**
     * Batch upload dengan progress tracking
     *
     * @param RajaGambar $model
     * @param array $files
     * @param string $collection
     * @param callable|null $progressCallback
     * @return \Illuminate\Support\Collection
     */
    function raja_gambar_batch_upload(RajaGambar $model, array $files, string $collection = 'default', ?callable $progressCallback = null)
    {
        return RajaGambarHelper::batchUpload($model, $files, $collection, $progressCallback);
    }
}

if (!function_exists('raja_gambar_collection_settings')) {
    /**
     * Get collection settings
     *
     * @param string $collection
     * @return array
     */
    function raja_gambar_collection_settings(string $collection): array
    {
        return RajaGambarHelper::getCollectionSettings($collection);
    }
}
