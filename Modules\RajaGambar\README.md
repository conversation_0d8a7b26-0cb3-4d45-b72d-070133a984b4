# RajaGambar Module

Modul RajaGambar menyediakan service dan custom FilamentPHP component untuk mengelola upload dan pemrosesan gambar menggunakan Spatie Media Library.

## Fitur

- **SpatieMediaService**: Service class untuk mengelola upload, resize, crop, dan optimasi gambar
- **RajaGambarUpload**: Custom FilamentPHP component dengan method chaining untuk konfigurasi yang mudah
- Integrasi penuh dengan Spatie Media Library
- Support multiple file upload
- Automatic image processing (resize, crop, optimize)
- Preview gambar dengan drag & drop interface
- Konfigurasi media collections yang fleksibel

## Instalasi

Modul ini sudah terintegrasi dengan sistem auto-discovery Laravel. Pastikan Spatie Media Library sudah terinstall:

```bash
composer require spatie/laravel-medialibrary
php artisan vendor:publish --provider="Spatie\MediaLibrary\MediaLibraryServiceProvider" --tag="migrations"
php artisan migrate
```

### Publish Konfigurasi (Opsional)

```bash
php artisan vendor:publish --tag="rajagambar-config"
```

File konfigurasi akan tersedia di `config/rajagambar.php` dengan berbagai pengaturan default untuk collections, image processing, dan lainnya.

## Penggunaan SpatieMediaService

### Basic Usage

```php
use Modules\RajaGambar\Services\SpatieMediaService;
use Modules\RajaGambar\Models\RajaGambar;

$mediaService = app(SpatieMediaService::class);
$rajaGambar = RajaGambar::find(1);

// Upload single file
$media = $mediaService->uploadMedia($rajaGambar, $uploadedFile, 'gallery');

// Upload multiple files
$mediaCollection = $mediaService->uploadMedia($rajaGambar, $uploadedFiles, 'gallery');

// Get media
$media = $mediaService->getMedia($rajaGambar, 'gallery');
$firstMedia = $mediaService->getFirstMedia($rajaGambar, 'gallery');

// Delete media
$mediaService->deleteMedia($media);
$mediaService->clearMediaCollection($rajaGambar, 'gallery');
```

## Penggunaan RajaGambarUpload Component

### Basic Usage

```php
use Modules\RajaGambar\Forms\Components\RajaGambarUpload;

// Basic upload
RajaGambarUpload::make('gambar')
    ->label('Upload Gambar')
    ->required()

// Dengan collection
RajaGambarUpload::make('foto_profil')
    ->label('Foto Profil')
    ->collection('avatars')
    ->maxFiles(1)
```

### Method Chaining Configuration

```php
RajaGambarUpload::make('banner')
    ->label('Banner Image')
    ->collection('banners')
    ->resize(1200, 600, 'cover')           // Resize ke 1200x600 dengan cover fit
    ->crop('16:9')                         // Crop dengan aspect ratio 16:9
    ->optimize(85)                         // Optimize dengan quality 85%
    ->multiple(true)                       // Allow multiple files
    ->maxFiles(5)                          // Max 5 files
    ->maxSize(5120)                        // Max 5MB per file
    ->preview('250px')                     // Preview height 250px
    ->directory('banners/2024')            // Custom directory
    ->disk('public')                       // Storage disk
    ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
```

## Available Methods

### RajaGambarUpload Methods

| Method | Description | Example |
|--------|-------------|---------|
| `resize(width, height, fit)` | Set resize dimensions | `->resize(800, 600, 'cover')` |
| `crop(ratio/width, height)` | Set crop ratio/dimensions | `->crop('16:9')` atau `->crop(400, 300)` |
| `optimize(quality)` | Enable optimization | `->optimize(85)` |
| `collection(name)` | Set media collection | `->collection('gallery')` |
| `multiple(bool)` | Enable multiple upload | `->multiple(true)` |
| `maxFiles(count)` | Set max files | `->maxFiles(5)` |
| `maxSize(kb)` | Set max file size | `->maxSize(5120)` |
| `directory(path)` | Set storage directory | `->directory('uploads/2024')` |
| `preview(height)` | Set preview height | `->preview('200px')` |

## Media Collections

Model RajaGambar sudah memiliki beberapa media collections yang terdefinisi:

- `default`: Single file, semua format gambar
- `gallery`: Multiple files, format gambar standar
- `avatars`: Single file, JPEG/PNG only
- `banners`: Multiple files, JPEG/PNG/WebP
- `products`: Multiple files, JPEG/PNG/WebP
- `cms`: Multiple files, gambar + PDF

## Contoh Implementasi dalam Resource

```php
use Modules\RajaGambar\Forms\Components\RajaGambarUpload;

public static function form(Form $form): Form
{
    return $form->schema([
        Section::make('Media')
            ->schema([
                RajaGambarUpload::make('foto_utama')
                    ->label('Foto Utama')
                    ->collection('default')
                    ->resize(800, 600)
                    ->optimize(85)
                    ->required(),

                RajaGambarUpload::make('galeri')
                    ->label('Galeri Foto')
                    ->collection('gallery')
                    ->resize(1024, 768)
                    ->crop('4:3')
                    ->optimize(80)
                    ->multiple()
                    ->maxFiles(8),
            ]),
    ]);
}
```

## Helper Functions

Modul ini menyediakan helper functions global untuk memudahkan penggunaan:

### Upload Functions

```php
// Upload gambar
$media = raja_gambar_upload($model, $files, 'gallery');

// Batch upload dengan progress callback
$mediaCollection = raja_gambar_batch_upload($model, $files, 'gallery', function($current, $total, $media) {
    echo "Uploading {$current}/{$total}: {$media->name}";
});
```

### Get Image Functions

```php
// Get image URL
$url = raja_gambar_url($model, 'gallery', 'thumb');

// Get multiple image URLs
$urls = raja_gambar_urls($model, 'gallery', 'medium');

// Get first image
$media = raja_gambar_first($model, 'gallery');

// Get all images
$mediaCollection = raja_gambar_all($model, 'gallery');
```

### Utility Functions

```php
// Check if model has images
if (raja_gambar_has($model, 'gallery')) {
    echo "Model has gallery images";
}

// Get image count
$count = raja_gambar_count($model, 'gallery');

// Get image info
$info = raja_gambar_info($media);

// Validate image file
if (raja_gambar_validate($uploadedFile)) {
    echo "Valid image file";
}
```

### HTML Generation Functions

```php
// Create single image HTML
echo raja_gambar_img($model, 'default', 'thumb', ['class' => 'img-fluid']);

// Create image gallery HTML
echo raja_gambar_gallery($model, 'gallery', 'thumb');

// Create responsive image HTML
echo raja_gambar_responsive($model, 'default', [
    'thumb' => '300w',
    'medium' => '600w',
    'large' => '1200w'
], ['class' => 'responsive-img']);
```

### Management Functions

```php
// Delete image
raja_gambar_delete($media);

// Clear all images from collection
raja_gambar_clear($model, 'gallery');

// Get configuration
$config = raja_gambar_config('defaults.image_quality', 80);

// Get collection settings
$settings = raja_gambar_collection_settings('gallery');

// Get supported formats
$formats = raja_gambar_formats();
```

## Konfigurasi

File konfigurasi `config/rajagambar.php` menyediakan berbagai pengaturan:

### Default Settings
- Collection default
- Disk penyimpanan
- Kualitas gambar
- Mode resize
- Enable conversions

### Media Collections
- Konfigurasi per collection (default, gallery, avatars, banners, products, cms)
- Accepted mime types
- Automatic conversions
- Single/multiple file settings

### Image Processing
- Default resize dimensions
- Crop ratios
- Quality levels
- Fit options

### Security Settings
- File type validation
- Extension blocking
- Filename sanitization
- File size limits

### Performance Settings
- Queue conversions
- Memory limits
- Timeout settings

## Changelog

Please see [CHANGELOG](CHANGELOG.md) for more information on what has changed recently.

## Security

Please see [SECURITY](SECURITY.md) for more information about security.

## License

The MIT License (MIT). Please see [License File](LICENSE.md) for more information.
