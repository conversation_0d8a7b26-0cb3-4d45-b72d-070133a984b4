<?php

namespace Modules\RajaGambar\Examples;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\RajaGambar\Forms\Components\RajaGambarUpload;
use Modules\RajaGambar\Models\RajaGambar;

/**
 * Contoh penggunaan RajaGambarUpload component dalam Filament Resource
 * 
 * File ini hanya sebagai contoh dan tidak akan digunakan dalam aplikasi.
 * Anda bisa menggunakan pola ini dalam Resource Anda sendiri.
 */
class ExampleResource extends Resource
{
    protected static ?string $model = RajaGambar::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    protected static ?string $navigationLabel = 'Contoh Raja Gambar';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Dasar')
                    ->schema([
                        Forms\Components\TextInput::make('nama')
                            ->label('Nama')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Textarea::make('deskripsi')
                            ->label('Deskripsi')
                            ->rows(3),

                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->options([
                                'aktif' => 'Aktif',
                                'nonaktif' => 'Non Aktif',
                            ])
                            ->default('aktif'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Media Upload')
                    ->description('Upload dan kelola gambar dengan berbagai konfigurasi')
                    ->schema([
                        // Basic upload
                        RajaGambarUpload::make('foto_utama')
                            ->label('Foto Utama')
                            ->collection('default')
                            ->resize(800, 600)
                            ->optimize(85)
                            ->required(),

                        // Gallery dengan multiple files
                        RajaGambarUpload::make('galeri')
                            ->label('Galeri Foto')
                            ->collection('gallery')
                            ->resize(1024, 768, 'cover')
                            ->crop('4:3')
                            ->optimize(80)
                            ->conversions(true)
                            ->addConversion('thumb', [
                                'width' => 300,
                                'height' => 225,
                                'fit' => 'crop'
                            ]),

                        // Avatar dengan crop square
                        RajaGambarUpload::make('avatar')
                            ->label('Avatar')
                            ->collection('avatars')
                            ->resize(400, 400)
                            ->crop('1:1')
                            ->optimize(90)
                            ->preview('150px'),

                        // Banner dengan aspect ratio
                        RajaGambarUpload::make('banner')
                            ->label('Banner')
                            ->collection('banners')
                            ->resize(1200, 400, 'cover')
                            ->crop('3:1')
                            ->optimize(75)
                            ->quality(85),

                        // Product images dengan custom conversions
                        RajaGambarUpload::make('produk_images')
                            ->label('Gambar Produk')
                            ->collection('products')
                            ->resize(800, 600)
                            ->crop('4:3')
                            ->optimize(80)
                            ->conversions(true)
                            ->addConversion('small', [
                                'width' => 200,
                                'height' => 150,
                                'fit' => 'crop'
                            ])
                            ->addConversion('medium', [
                                'width' => 400,
                                'height' => 300,
                                'fit' => 'contain'
                            ])
                            ->addConversion('large', [
                                'width' => 800,
                                'height' => 600,
                                'fit' => 'cover'
                            ]),
                    ])
                    ->columns(1),

                Forms\Components\Section::make('Advanced Upload Examples')
                    ->description('Contoh konfigurasi lanjutan')
                    ->schema([
                        // CMS content dengan mixed media
                        RajaGambarUpload::make('cms_media')
                            ->label('Media CMS')
                            ->collection('cms')
                            ->resize(1024, 768)
                            ->optimize(85),

                        // Custom collection dengan specific settings
                        RajaGambarUpload::make('custom_media')
                            ->label('Media Custom')
                            ->collection('custom')
                            ->resize(600, 400, 'contain')
                            ->crop('3:2')
                            ->optimize(90)
                            ->quality(95)
                            ->conversions(false), // Disable automatic conversions
                    ])
                    ->columns(2)
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nama')
                    ->label('Nama')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\ImageColumn::make('foto_utama')
                    ->label('Foto Utama')
                    ->circular()
                    ->size(60),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'aktif' => 'success',
                        'nonaktif' => 'danger',
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Diperbarui')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'aktif' => 'Aktif',
                        'nonaktif' => 'Non Aktif',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListExamples::route('/'),
            'create' => Pages\CreateExample::route('/create'),
            'edit' => Pages\EditExample::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return 'Raja Gambar';
    }

    public static function getNavigationSort(): ?int
    {
        return 2;
    }
}

/**
 * Contoh penggunaan dalam form yang lebih sederhana
 */
class SimpleExampleForm
{
    public static function getFormSchema(): array
    {
        return [
            // Upload gambar sederhana
            RajaGambarUpload::make('gambar')
                ->label('Upload Gambar')
                ->required(),

            // Upload dengan resize
            RajaGambarUpload::make('foto')
                ->label('Foto')
                ->resize(800, 600)
                ->optimize(),

            // Upload multiple dengan crop
            RajaGambarUpload::make('gallery')
                ->label('Galeri')
                ->collection('gallery')
                ->crop('16:9')
                ->optimize(85),

            // Upload avatar dengan square crop
            RajaGambarUpload::make('avatar')
                ->label('Avatar')
                ->collection('avatars')
                ->resize(300, 300)
                ->crop('1:1')
                ->optimize(90),
        ];
    }
}

/**
 * Contoh method chaining yang tersedia
 */
class MethodChainingExamples
{
    public static function getAllMethods()
    {
        return [
            // Basic configuration
            'basic' => RajaGambarUpload::make('basic')
                ->label('Basic Upload')
                ->collection('default'),

            // Resize configuration
            'resize' => RajaGambarUpload::make('resize')
                ->resize(800, 600, 'cover'),

            // Crop configuration
            'crop_ratio' => RajaGambarUpload::make('crop_ratio')
                ->crop('16:9'),

            'crop_dimensions' => RajaGambarUpload::make('crop_dimensions')
                ->crop(400, 300),

            // Optimization
            'optimize' => RajaGambarUpload::make('optimize')
                ->optimize(85),

            // Quality setting
            'quality' => RajaGambarUpload::make('quality')
                ->quality(90),

            // Conversions
            'conversions' => RajaGambarUpload::make('conversions')
                ->conversions(true)
                ->addConversion('thumb', ['width' => 150, 'height' => 150])
                ->addConversion('medium', ['width' => 400, 'height' => 300]),

            // Preview
            'preview' => RajaGambarUpload::make('preview')
                ->preview('200px'),

            // Combined configuration
            'combined' => RajaGambarUpload::make('combined')
                ->label('Combined Example')
                ->collection('products')
                ->resize(1024, 768, 'cover')
                ->crop('4:3')
                ->optimize(80)
                ->quality(85)
                ->conversions(true)
                ->addConversion('thumb', ['width' => 200, 'height' => 150])
                ->preview('250px'),
        ];
    }
}
