<?php

namespace Modules\RajaGambar\Helpers;

use Modules\RajaGambar\Services\SpatieMediaService;
use Modules\RajaGambar\Models\RajaGambar;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * Helper class untuk mempermudah penggunaan <PERSON>
 */
class RajaGambarHelper
{
    protected static ?SpatieMediaService $mediaService = null;

    /**
     * Get media service instance
     */
    protected static function getMediaService(): SpatieMediaService
    {
        if (static::$mediaService === null) {
            static::$mediaService = app(SpatieMediaService::class);
        }

        return static::$mediaService;
    }

    /**
     * Upload gambar dengan konfigurasi sederhana
     *
     * @param RajaGambar $model
     * @param mixed $files
     * @param string $collection
     * @param array $options
     * @return Media|\Illuminate\Support\Collection
     */
    public static function upload(RajaGambar $model, $files, string $collection = 'default', array $options = [])
    {
        return static::getMediaService()->uploadMedia($model, $files, $collection, $options);
    }

    /**
     * Get gambar dari model
     *
     * @param RajaGambar $model
     * @param string $collection
     * @return \Illuminate\Support\Collection
     */
    public static function getImages(RajaGambar $model, string $collection = 'default')
    {
        return static::getMediaService()->getMedia($model, $collection);
    }

    /**
     * Get gambar pertama dari collection
     *
     * @param RajaGambar $model
     * @param string $collection
     * @return Media|null
     */
    public static function getFirstImage(RajaGambar $model, string $collection = 'default'): ?Media
    {
        return static::getMediaService()->getFirstMedia($model, $collection);
    }

    /**
     * Get URL gambar dengan conversion
     *
     * @param RajaGambar $model
     * @param string $collection
     * @param string $conversion
     * @return string|null
     */
    public static function getImageUrl(RajaGambar $model, string $collection = 'default', string $conversion = ''): ?string
    {
        $media = static::getFirstImage($model, $collection);
        
        if (!$media) {
            return null;
        }

        return static::getMediaService()->getMediaUrl($media, $conversion);
    }

    /**
     * Get multiple image URLs
     *
     * @param RajaGambar $model
     * @param string $collection
     * @param string $conversion
     * @return array
     */
    public static function getImageUrls(RajaGambar $model, string $collection = 'default', string $conversion = ''): array
    {
        $mediaCollection = static::getImages($model, $collection);
        $urls = [];

        foreach ($mediaCollection as $media) {
            $urls[] = static::getMediaService()->getMediaUrl($media, $conversion);
        }

        return $urls;
    }

    /**
     * Delete gambar
     *
     * @param Media $media
     * @return bool
     */
    public static function deleteImage(Media $media): bool
    {
        return static::getMediaService()->deleteMedia($media);
    }

    /**
     * Clear semua gambar dari collection
     *
     * @param RajaGambar $model
     * @param string $collection
     * @return bool
     */
    public static function clearImages(RajaGambar $model, string $collection): bool
    {
        return static::getMediaService()->clearMediaCollection($model, $collection);
    }

    /**
     * Check apakah model memiliki gambar
     *
     * @param RajaGambar $model
     * @param string $collection
     * @return bool
     */
    public static function hasImages(RajaGambar $model, string $collection = 'default'): bool
    {
        return static::getImages($model, $collection)->isNotEmpty();
    }

    /**
     * Get jumlah gambar dalam collection
     *
     * @param RajaGambar $model
     * @param string $collection
     * @return int
     */
    public static function getImageCount(RajaGambar $model, string $collection = 'default'): int
    {
        return static::getImages($model, $collection)->count();
    }

    /**
     * Get supported image formats
     *
     * @return array
     */
    public static function getSupportedFormats(): array
    {
        return static::getMediaService()->getSupportedFormats();
    }

    /**
     * Validate image file
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @return bool
     */
    public static function isValidImage($file): bool
    {
        return static::getMediaService()->isValidImage($file);
    }

    /**
     * Create image gallery HTML
     *
     * @param RajaGambar $model
     * @param string $collection
     * @param string $conversion
     * @param array $options
     * @return string
     */
    public static function createGallery(RajaGambar $model, string $collection = 'gallery', string $conversion = 'thumb', array $options = []): string
    {
        $images = static::getImages($model, $collection);
        
        if ($images->isEmpty()) {
            return '';
        }

        $html = '<div class="raja-gambar-gallery">';
        
        foreach ($images as $media) {
            $url = static::getMediaService()->getMediaUrl($media, $conversion);
            $fullUrl = static::getMediaService()->getMediaUrl($media);
            $alt = $media->name ?? 'Image';
            
            $html .= sprintf(
                '<div class="gallery-item"><a href="%s" data-lightbox="gallery"><img src="%s" alt="%s" class="gallery-thumb"></a></div>',
                $fullUrl,
                $url,
                htmlspecialchars($alt)
            );
        }
        
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Create single image HTML
     *
     * @param RajaGambar $model
     * @param string $collection
     * @param string $conversion
     * @param array $attributes
     * @return string
     */
    public static function createImage(RajaGambar $model, string $collection = 'default', string $conversion = '', array $attributes = []): string
    {
        $media = static::getFirstImage($model, $collection);
        
        if (!$media) {
            return '';
        }

        $url = static::getMediaService()->getMediaUrl($media, $conversion);
        $alt = $media->name ?? 'Image';
        
        $attributeString = '';
        foreach ($attributes as $key => $value) {
            $attributeString .= sprintf(' %s="%s"', $key, htmlspecialchars($value));
        }
        
        return sprintf('<img src="%s" alt="%s"%s>', $url, htmlspecialchars($alt), $attributeString);
    }

    /**
     * Get image info
     *
     * @param Media $media
     * @return array
     */
    public static function getImageInfo(Media $media): array
    {
        return [
            'id' => $media->id,
            'name' => $media->name,
            'file_name' => $media->file_name,
            'mime_type' => $media->mime_type,
            'size' => $media->size,
            'url' => $media->getUrl(),
            'collection' => $media->collection_name,
            'custom_properties' => $media->custom_properties,
            'created_at' => $media->created_at,
            'updated_at' => $media->updated_at,
        ];
    }

    /**
     * Batch upload dengan progress tracking
     *
     * @param RajaGambar $model
     * @param array $files
     * @param string $collection
     * @param callable|null $progressCallback
     * @return \Illuminate\Support\Collection
     */
    public static function batchUpload(RajaGambar $model, array $files, string $collection = 'default', ?callable $progressCallback = null)
    {
        $uploadedMedia = collect();
        $total = count($files);
        
        foreach ($files as $index => $file) {
            $media = static::upload($model, $file, $collection);
            $uploadedMedia->push($media);
            
            if ($progressCallback) {
                $progressCallback($index + 1, $total, $media);
            }
        }
        
        return $uploadedMedia;
    }

    /**
     * Get configuration from config file
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function config(string $key, $default = null)
    {
        return config("rajagambar.{$key}", $default);
    }

    /**
     * Get default collection settings
     *
     * @param string $collection
     * @return array
     */
    public static function getCollectionSettings(string $collection): array
    {
        return static::config("collections.{$collection}", []);
    }

    /**
     * Create responsive image HTML
     *
     * @param RajaGambar $model
     * @param string $collection
     * @param array $conversions
     * @param array $attributes
     * @return string
     */
    public static function createResponsiveImage(RajaGambar $model, string $collection = 'default', array $conversions = [], array $attributes = []): string
    {
        $media = static::getFirstImage($model, $collection);
        
        if (!$media) {
            return '';
        }

        $srcset = [];
        foreach ($conversions as $conversion => $descriptor) {
            $url = static::getMediaService()->getMediaUrl($media, $conversion);
            $srcset[] = "{$url} {$descriptor}";
        }

        $src = static::getMediaService()->getMediaUrl($media);
        $alt = $media->name ?? 'Image';
        
        $attributeString = '';
        foreach ($attributes as $key => $value) {
            $attributeString .= sprintf(' %s="%s"', $key, htmlspecialchars($value));
        }
        
        $srcsetString = !empty($srcset) ? ' srcset="' . implode(', ', $srcset) . '"' : '';
        
        return sprintf('<img src="%s" alt="%s"%s%s>', $src, htmlspecialchars($alt), $srcsetString, $attributeString);
    }
}
